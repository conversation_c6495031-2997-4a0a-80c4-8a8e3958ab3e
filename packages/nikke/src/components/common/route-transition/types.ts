export type TransitionDirection = "forward" | "back" | "none";

export interface RouteTransitionProps {
  /** 过渡动画持续时间（毫秒） */
  duration?: number;
  /** 缓动函数 */
  easing?: string;
  /** 是否启用过渡动画 */
  enabled?: boolean;
}

export interface TransitionState {
  /** 当前过渡方向 */
  direction: TransitionDirection;
  /** 是否正在过渡中 */
  is_transitioning: boolean;
  /** 前一个路由的组件 */
  prevComponent: any;
  /** 当前路由的组件 */
  currentComponent: any;
}
