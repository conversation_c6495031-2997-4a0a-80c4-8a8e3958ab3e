import { computed, ref, toValue, type MaybeRefOrGetter } from "vue";
import { storeToRefs } from "pinia";
import { usePostList } from "@/api/post";
import { useHomeStore } from "@/store/home/<USER>";
import { useLanguageStore } from "@/store/language";
import { useTanstackInfiniteList } from "./use-tanstack-infinite-list";
import { dealPostData } from "@/utils/home";
import { getStandardizedGameId, getStandardizedLang } from "packages/utils/standard";
import { filterCmsItem } from "packages/utils/cms";
import type { PostItem, OrderBy } from "packages/types/post";

import { getTags, getDistrictList } from "@/api/home";

export interface UseHomePlateListOptions {
  /**
   * 默认排序方式
   */
  defaultOrderBy?: OrderBy;
  /**
   * 是否启用金刚区功能
   */
  enableDistrict?: boolean;
  /**
   * 是否启用标签功能
   */
  enableTags?: boolean;
  /**
   * 是否立即加载数据
   */
  enabled?: MaybeRefOrGetter<boolean>;
  /**
   * 额外的请求参数
   */
  extraParams?: Record<string, any>;
}

/**
 * 首页板块列表通用 Composable
 * 统一管理帖子列表、标签、金刚区等功能
 */
export const useHomePlateList = (options: UseHomePlateListOptions = {}) => {
  const {
    defaultOrderBy = 2,
    enableDistrict = false,
    enableTags = true,
    enabled = true,
    extraParams = {},
  } = options;

  // 依赖的 store
  const { activeId: plateId, activeKey } = storeToRefs(useHomeStore());
  const { lang_regions } = storeToRefs(useLanguageStore());

  // 筛选状态
  const orderBy = ref<OrderBy>(defaultOrderBy);
  const activeTag = ref<string>("");

  // 标签相关 - 使用 TanStack Query
  const { data: tagsData, isLoading: tagLoading } = getTags(
    computed(() => ({
      plate_id: Number(plateId.value),
      limit: 100,
    })),
    {
      enabled: computed(() => enableTags && !!plateId.value),
      staleTime: 1000 * 60 * 10, // 10分钟缓存
      gcTime: 1000 * 60 * 15, // 15分钟垃圾回收
    },
  );

  const tagList = computed(() => {
    return (
      tagsData.value?.list.map((item) => ({
        id: item.id,
        tag_name: item.tag_name,
      })) || []
    );
  });

  // 金刚区相关 - 使用 TanStack Query
  const { data: districtData, isLoading: districtLoading } = getDistrictList(
    computed(() => ({
      plate_id: Number(plateId.value),
      limit: 10,
    })),
    {
      enabled: computed(() => enableDistrict && !!plateId.value),
      staleTime: 1000 * 60 * 10, // 10分钟缓存
      gcTime: 1000 * 60 * 15, // 15分钟垃圾回收
    },
  );

  const districtList = computed(() => {
    if (!districtData.value?.list) return [];

    const game_id = getStandardizedGameId();
    const lang = getStandardizedLang() || "en";
    return districtData.value.list
      .sort((x, y) => Number(x.order) - Number(y.order))
      .filter((item) => filterCmsItem({ ext_info: item.ext_info, lang, game_id }));
  });

  // 帖子列表
  const {
    list: postList,
    loading: postLoading,
    finished: postFinished,
    empty: postEmpty,
    load: loadMore,
    reset: resetPostList,
  } = useTanstackInfiniteList<PostItem>({
    queryKey: computed(() => [
      "homePlatePostList",
      plateId.value,
      activeKey.value,
      orderBy.value,
      activeTag.value,
      lang_regions.value,
    ]),
    queryFn: async (next_page_cursor) => {
      const { list, page_info } = await usePostList.run({
        search_type: +activeTag.value ? 1 : 0, // 选择了tag，search_type=1
        plate_id: Number(plateId.value),
        plate_unique_id: activeKey.value,
        nextPageCursor: next_page_cursor,
        order_by: orderBy.value,
        tag_id: +activeTag.value || undefined,
        limit: "10",
        regions: lang_regions.value,
        ...extraParams, // 支持额外参数
      });

      // 处理数据
      const processedList = dealPostData(list);

      return {
        list: processedList,
        page_info,
      };
    },
    item_key: "post_uuid",
    enabled: computed(() => {
      const enabledValue = toValue(enabled);
      return enabledValue && !!plateId.value && !!activeKey.value;
    }),
    staleTime: 1000 * 60 * 5, // 5分钟缓存
    gcTime: 1000 * 60 * 10, // 10分钟垃圾回收
  });

  // 切换排序
  const changeOrderBy = (newOrderBy: OrderBy) => {
    orderBy.value = newOrderBy;
    resetPostList();
  };

  // 切换标签
  const changeTag = (tagId: string) => {
    activeTag.value = tagId;
    resetPostList();
  };

  // 重置所有状态
  const reset = () => {
    activeTag.value = "";
    orderBy.value = defaultOrderBy;
    resetPostList();
  };

  // 初始化数据 - TanStack Query 会自动加载数据，这里只是为了兼容性
  const init = async () => {
    // TanStack Query 会根据 enabled 条件自动加载数据
    // 这里不需要手动调用，保留方法只是为了向后兼容
  };

  return {
    // 帖子列表相关
    postList,
    postLoading,
    postFinished,
    postEmpty,
    loadMore,
    resetPostList,

    // 筛选状态
    orderBy,
    activeTag,
    changeOrderBy,
    changeTag,

    // 标签相关
    tagList,
    tagLoading,

    // 金刚区相关
    districtList,
    districtLoading,

    // 通用方法
    init,
    reset,
  };
};
