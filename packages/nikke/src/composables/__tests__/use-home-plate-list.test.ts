import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ref } from 'vue';
import { useHomePlateList } from '../use-home-plate-list';

// Mock dependencies
vi.mock('@/store/home/<USER>', () => ({
  useHomeStore: () => ({
    activeId: ref('1'),
    activeKey: ref('recommend'),
  }),
}));

vi.mock('@/store/language', () => ({
  useLanguageStore: () => ({
    lang_regions: ref(['en']),
  }),
}));

vi.mock('../use-tanstack-infinite-list', () => ({
  useTanstackInfiniteList: vi.fn(() => ({
    list: ref([]),
    loading: ref(false),
    finished: ref(false),
    empty: ref(true),
    load: vi.fn(),
    reset: vi.fn(),
  })),
}));

vi.mock('@/api/home', () => ({
  getTags: {
    run: vi.fn(() => Promise.resolve({ list: [] })),
  },
  getDistrictList: {
    run: vi.fn(() => Promise.resolve({ list: [] })),
  },
}));

describe('useHomePlateList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with default options', () => {
    const result = useHomePlateList();
    
    expect(result).toHaveProperty('postList');
    expect(result).toHaveProperty('postLoading');
    expect(result).toHaveProperty('loadMore');
    expect(result).toHaveProperty('orderBy');
    expect(result).toHaveProperty('activeTag');
    expect(result).toHaveProperty('changeOrderBy');
    expect(result).toHaveProperty('changeTag');
    expect(result).toHaveProperty('init');
    expect(result).toHaveProperty('reset');
  });

  it('should support custom options', () => {
    const result = useHomePlateList({
      defaultOrderBy: 1,
      enableDistrict: true,
      enableTags: false,
      extraParams: { platform: 'youtube' },
    });
    
    expect(result.orderBy.value).toBe(1);
  });

  it('should provide tag-related functionality when enabled', () => {
    const result = useHomePlateList({
      enableTags: true,
    });
    
    expect(result).toHaveProperty('tagList');
    expect(result).toHaveProperty('getTagData');
  });

  it('should provide district-related functionality when enabled', () => {
    const result = useHomePlateList({
      enableDistrict: true,
    });
    
    expect(result).toHaveProperty('districtList');
    expect(result).toHaveProperty('getDistrictData');
  });
});
