# useHomePlateList Composable

这是一个统一的首页板块列表管理 composable，用于替代之前分散的多个 plate store。

## 特性

- ✅ **充分利用 TanStack Query**：自动缓存、重试、错误处理
- ✅ **统一的 API**：所有板块使用相同的接口
- ✅ **灵活配置**：支持自定义排序、标签、金刚区等功能
- ✅ **TypeScript 支持**：完整的类型推导
- ✅ **自动数据处理**：去重、格式化等

## 基本用法

```typescript
import { useHomePlateList } from '@/composables/use-home-plate-list';

// 最简单的用法
const { postList, postLoading, loadMore } = useHomePlateList();
```

## 配置选项

```typescript
const {
  postList,
  postLoading,
  loadMore,
  orderBy,
  changeOrderBy,
  activeTag,
  changeTag,
  tagList,
  districtList,
  init,
  reset,
} = useHomePlateList({
  // 默认排序方式：1=时间排序，2=热度排序
  defaultOrderBy: 2,
  
  // 是否启用金刚区功能
  enableDistrict: true,
  
  // 是否启用标签功能
  enableTags: true,
  
  // 是否立即加载数据
  enabled: true,
  
  // 额外的请求参数（如 event 板块的 platform: "youtube"）
  extraParams: {
    platform: "youtube"
  }
});
```

## 各板块的推荐配置

### Recommend（推荐）
```typescript
const config = {
  defaultOrderBy: 2, // 热度排序
  enableDistrict: true, // 启用金刚区
  enableTags: false, // 不使用标签
};
```

### Official（官方）
```typescript
const config = {
  defaultOrderBy: 1, // 时间排序
  enableDistrict: true, // 启用金刚区
  enableTags: true, // 启用标签
};
```

### Guides（攻略）
```typescript
const config = {
  defaultOrderBy: 2, // 热度排序
  enableDistrict: false, // 不启用金刚区
  enableTags: true, // 启用标签
};
```

### Outpost（前哨站）
```typescript
const config = {
  defaultOrderBy: 2, // 热度排序
  enableDistrict: true, // 启用金刚区
  enableTags: true, // 启用标签
};
```

### NikkeArt（艺术）
```typescript
const config = {
  defaultOrderBy: 2, // 热度排序
  enableDistrict: false, // 不启用金刚区
  enableTags: true, // 启用标签
};
```

### Event（活动）
```typescript
const config = {
  defaultOrderBy: 1, // 时间排序
  enableDistrict: false, // 不启用金刚区
  enableTags: false, // 不启用标签
  extraParams: {
    platform: "youtube" // 特殊参数
  }
};
```

## 返回值说明

| 属性 | 类型 | 说明 |
|------|------|------|
| `postList` | `Ref<PostItem[]>` | 帖子列表 |
| `postLoading` | `Ref<boolean>` | 加载状态 |
| `postFinished` | `Ref<boolean>` | 是否加载完成 |
| `postEmpty` | `Ref<boolean>` | 是否为空 |
| `loadMore` | `Function` | 加载更多 |
| `resetPostList` | `Function` | 重置列表 |
| `orderBy` | `Ref<OrderBy>` | 当前排序方式 |
| `changeOrderBy` | `Function` | 切换排序 |
| `activeTag` | `Ref<string>` | 当前标签 |
| `changeTag` | `Function` | 切换标签 |
| `tagList` | `ComputedRef<Tag[]>` | 标签列表 |
| `tagLoading` | `Ref<boolean>` | 标签加载状态 |
| `districtList` | `ComputedRef<District[]>` | 金刚区列表 |
| `districtLoading` | `Ref<boolean>` | 金刚区加载状态 |
| `init` | `Function` | 初始化（兼容性方法） |
| `reset` | `Function` | 重置所有状态 |

## 缓存策略

- **帖子列表**：5分钟 staleTime，10分钟 gcTime
- **标签列表**：10分钟 staleTime，15分钟 gcTime  
- **金刚区列表**：10分钟 staleTime，15分钟 gcTime

## 迁移指南

### 从旧的 store 迁移

**之前：**
```typescript
// 旧的方式
import { useHomeRecommendStore } from '@/store/home/<USER>';

const { getPostList, getTagData, getDistrictData } = useHomeRecommendStore();
const { postList, postLoading, pageInfo } = storeToRefs(useHomeRecommendStore());

onMounted(() => {
  getTagData();
  getDistrictData();
  getPostList();
});
```

**现在：**
```typescript
// 新的方式
import { useHomePlateList } from '@/composables/use-home-plate-list';

const { postList, postLoading, loadMore, init } = useHomePlateList({
  defaultOrderBy: 2,
  enableDistrict: true,
  enableTags: false,
});

onMounted(async () => {
  await init(); // 可选，数据会自动加载
});
```

## 优势

1. **减少重复代码**：从 6 个 store（~600 行）合并为 1 个 composable（~200 行）
2. **更好的缓存**：充分利用 TanStack Query 的缓存机制
3. **更好的错误处理**：自动重试和错误恢复
4. **更好的类型支持**：完整的 TypeScript 类型推导
5. **更容易维护**：统一的逻辑，修改一处即可
